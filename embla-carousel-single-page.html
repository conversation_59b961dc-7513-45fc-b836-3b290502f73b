
  <!-- Embla Carousel CSS and JS -->
  <script src="https://cdn.jsdelivr.net/npm/embla-carousel@8.6.0/embla-carousel.umd.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/embla-carousel-autoplay@8.6.0/embla-carousel-autoplay.umd.js"></script>
  
  <style>
    *,
    *::before,
    *::after {
      box-sizing: inherit;
    }
    /* Theme Variables */
    .theme-light {
      --brand-primary: rgb(47, 112, 193);
      --brand-secondary: rgb(116, 97, 195);
      --brand-alternative: rgb(19, 120, 134);
      --background-site: rgb(249, 249, 249);
      --background-code: rgb(244, 244, 244);
      --text-body: rgb(54, 49, 61);
      --text-comment: rgb(99, 94, 105);
      --text-high-contrast: rgb(49, 49, 49);
      --text-medium-contrast: rgb(99, 94, 105);
      --text-low-contrast: rgb(116, 109, 118);
      --detail-high-contrast: rgb(192, 192, 192);
      --detail-medium-contrast: rgb(234, 234, 234);
      --detail-low-contrast: rgb(240, 240, 242);
      --admonition-note: rgb(46, 109, 188);
      --admonition-warning: rgb(255, 196, 9);
      --admonition-danger: rgb(220, 38, 38);
      --brand-primary-rgb-value: 47, 112, 193;
      --brand-secondary-rgb-value: 116, 97, 195;
      --brand-alternative-rgb-value: 19, 120, 134;
      --background-site-rgb-value: 249, 249, 249;
      --background-code-rgb-value: 244, 244, 244;
      --text-body-rgb-value: 54, 49, 61;
      --text-comment-rgb-value: 99, 94, 105;
      --text-high-contrast-rgb-value: 49, 49, 49;
      --text-medium-contrast-rgb-value: 99, 94, 105;
      --text-low-contrast-rgb-value: 116, 109, 118;
      --detail-high-contrast-rgb-value: 192, 192, 192;
      --detail-medium-contrast-rgb-value: 234, 234, 234;
      --detail-low-contrast-rgb-value: 240, 240, 242;
      --admonition-note-rgb-value: 46, 109, 188;
      --admonition-warning-rgb-value: 255, 196, 9;
      --admonition-danger-rgb-value: 220, 38, 38;
    }
    .theme-dark {
      --brand-primary: rgb(138, 180, 248);
      --brand-secondary: rgb(193, 168, 226);
      --brand-alternative: rgb(136, 186, 191);
      --background-site: rgb(0, 0, 0);
      --background-code: rgb(12, 12, 12);
      --text-body: rgb(222, 222, 222);
      --text-comment: rgb(170, 170, 170);
      --text-high-contrast: rgb(230, 230, 230);
      --text-medium-contrast: rgb(202, 202, 202);
      --text-low-contrast: rgb(170, 170, 170);
      --detail-high-contrast: rgb(101, 101, 101);
      --detail-medium-contrast: rgb(25, 25, 25);
      --detail-low-contrast: rgb(21, 21, 21);
      --admonition-note: rgb(138, 180, 248);
      --admonition-warning: rgb(253, 186, 116);
      --admonition-danger: rgb(220, 38, 38);
      --brand-primary-rgb-value: 138, 180, 248;
      --brand-secondary-rgb-value: 193, 168, 226;
      --brand-alternative-rgb-value: 136, 186, 191;
      --background-site-rgb-value: 0, 0, 0;
      --background-code-rgb-value: 12, 12, 12;
      --text-body-rgb-value: 222, 222, 222;
      --text-comment-rgb-value: 170, 170, 170;
      --text-high-contrast-rgb-value: 230, 230, 230;
      --text-medium-contrast-rgb-value: 202, 202, 202;
      --text-low-contrast-rgb-value: 170, 170, 170;
      --detail-high-contrast-rgb-value: 101, 101, 101;
      --detail-medium-contrast-rgb-value: 25, 25, 25;
      --detail-low-contrast-rgb-value: 21, 21, 21;
      --admonition-note-rgb-value: 138, 180, 248;
      --admonition-warning-rgb-value: 253, 186, 116;
      --admonition-danger-rgb-value: 220, 38, 38;
    }

    /* Embla Carousel Styles */
    .embla {
      max-width: 100%;
      width: 100%;
      margin: auto;
      --slide-height: 19rem;
      --slide-spacing: 1rem;
      --slide-size: 100%;
    }
    .embla__viewport {
      overflow: hidden;
      padding: 0px 
    }
    .embla__container {
      display: flex;
      touch-action: pan-y pinch-zoom;
      margin-left: calc(var(--slide-spacing) * -1);
    }
    .embla__slide {
      transform: translate3d(0, 0, 0);
      flex: 0 0 var(--slide-size);
      min-width: 0;
      padding-left: var(--slide-spacing);
    }
    .embla__slide__number {
      box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
      border-radius: 1.8rem;
      font-size: 4rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      height: var(--slide-height);
      user-select: none;
   padding: 16px 32px;
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-radius: 16px !important;
      color: white;
      font-size: 18px;
      font-weight: 500;
      text-decoration: none !important;
      cursor: pointer;
      backdrop-filter: blur(7px);
      -webkit-backdrop-filter: blur(7px);
      transition: all 0.3s ease;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    .embla__controls {
      display: grid;
      grid-template-columns: auto 1fr;
      justify-content: space-between;
      gap: 1.2rem;
      margin-top: 1.8rem;
    }
    .embla__buttons {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.6rem;
      align-items: center;
    }
    .embla__button {
      -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
      -webkit-appearance: none;
      appearance: none;
      background-color: transparent;
      touch-action: manipulation;
      display: inline-flex;
      text-decoration: none;
      cursor: pointer;
      border: 0;
      padding: 0;
      margin: 0;
      box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
      width: 3.6rem;
      height: 3.6rem;
      z-index: 1;
      border-radius: 50%;
      color: var(--text-body);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .embla__button:disabled {
      color: var(--detail-high-contrast);
    }
    .embla__button__svg {
      width: 35%;
      height: 35%;
    }
    .embla__dots {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      align-items: center;
      margin-right: calc((2.6rem - 1.4rem) / 2 * -1);
      display: none !important;
    }
    .embla__dot {
      display: none;
      -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
      -webkit-appearance: none;
      appearance: none;
      background-color: transparent;
      touch-action: manipulation;
      display: inline-flex;
      text-decoration: none;
      cursor: pointer;
      border: 0;
      padding: 0;
      margin: 0;
      width: 2.6rem;
      height: 2.6rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }
    .embla__dot:after {
      box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
      width: 1.4rem;
      height: 1.4rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      content: '';
      border: 1px solid rgba(255,255,255,0.4);
      display: none;
    }
    .embla__dot--selected:after {
      box-shadow: inset 0 0 0 0.2rem var(--text-body);
    }
  </style>


  <section class="embla">
    <div class="embla__viewport">
      <div class="embla__container">
        <div class="embla__slide">
          <div class="embla__slide__number">1</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">2</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">3</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">4</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">5</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">1</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">2</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">3</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">4</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">5</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">1</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">2</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">3</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">4</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">5</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">1</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">2</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">3</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">4</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">5</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">1</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">2</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">3</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">4</div>
        </div>
        <div class="embla__slide">
          <div class="embla__slide__number">5</div>
        </div>
      </div>
    </div>

    <div class="embla__controls">
      <div class="embla__buttons">
        <button class="embla__button embla__button--prev" type="button">
          <svg class="embla__button__svg" viewBox="0 0 532 532">
            <path
              fill="currentColor"
              d="M355.66 11.354c13.793-13.805 36.208-13.805 50.001 0 13.785 13.804 13.785 36.238 0 50.034L201.22 266l204.442 204.61c13.785 13.805 13.785 36.239 0 50.044-13.793 13.796-36.208 13.796-50.002 0a5994246.277 5994246.277 0 0 0-229.332-229.454 35.065 35.065 0 0 1-10.326-25.126c0-9.2 3.393-18.26 10.326-25.2C172.192 194.973 332.731 34.31 355.66 11.354Z"
            ></path>
          </svg>
        </button>

        <button class="embla__button embla__button--next" type="button">
          <svg class="embla__button__svg" viewBox="0 0 532 532">
            <path
              fill="currentColor"
              d="M176.34 520.646c-13.793 13.805-36.208 13.805-50.001 0-13.785-13.804-13.785-36.238 0-50.034L330.78 266 126.34 61.391c-13.785-13.805-13.785-36.239 0-50.044 13.793-13.796 36.208-13.796 50.002 0 22.928 22.947 206.395 206.507 229.332 229.454a35.065 35.065 0 0 1 10.326 25.126c0 9.2-3.393 18.26-10.326 25.2-45.865 45.901-206.404 206.564-229.332 229.52Z"
            ></path>
          </svg>
        </button>
      </div>

      <div class="embla__dots"></div>
    </div>
  </section>

  <noscript>You need to enable JavaScript to view this.</noscript>

  <script>
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
      // Arrow Button Functions
      function addTogglePrevNextBtnsActive(emblaApi, prevBtn, nextBtn) {
        const togglePrevNextBtnsState = () => {
          if (emblaApi.canScrollPrev()) prevBtn.removeAttribute('disabled')
          else prevBtn.setAttribute('disabled', 'disabled')

          if (emblaApi.canScrollNext()) nextBtn.removeAttribute('disabled')
          else nextBtn.setAttribute('disabled', 'disabled')
        }

        emblaApi
          .on('select', togglePrevNextBtnsState)
          .on('init', togglePrevNextBtnsState)
          .on('reInit', togglePrevNextBtnsState)

        return () => {
          prevBtn.removeAttribute('disabled')
          nextBtn.removeAttribute('disabled')
        }
      }

      function addPrevNextBtnsClickHandlers(emblaApi, prevBtn, nextBtn, onButtonClick) {
        const scrollPrev = () => {
          emblaApi.scrollPrev()
          if (onButtonClick) onButtonClick(emblaApi)
        }
        const scrollNext = () => {
          emblaApi.scrollNext()
          if (onButtonClick) onButtonClick(emblaApi)
        }
        prevBtn.addEventListener('click', scrollPrev, false)
        nextBtn.addEventListener('click', scrollNext, false)

        const removeTogglePrevNextBtnsActive = addTogglePrevNextBtnsActive(
          emblaApi,
          prevBtn,
          nextBtn
        )

        return () => {
          removeTogglePrevNextBtnsActive()
          prevBtn.removeEventListener('click', scrollPrev, false)
          nextBtn.removeEventListener('click', scrollNext, false)
        }
      }

      // Dot Button Functions
      function addDotBtnsAndClickHandlers(emblaApi, dotsNode, onButtonClick) {
        let dotNodes = []

        const addDotBtnsWithClickHandlers = () => {
          dotsNode.innerHTML = emblaApi
            .scrollSnapList()
            .map(() => '<button class="embla__dot" type="button"></button>')
            .join('')

          const scrollTo = (index) => {
            emblaApi.scrollTo(index)
            if (onButtonClick) onButtonClick(emblaApi)
          }

          dotNodes = Array.from(dotsNode.querySelectorAll('.embla__dot'))
          dotNodes.forEach((dotNode, index) => {
            dotNode.addEventListener('click', () => scrollTo(index), false)
          })
        }

        const toggleDotBtnsActive = () => {
          const previous = emblaApi.previousScrollSnap()
          const selected = emblaApi.selectedScrollSnap()
          dotNodes[previous].classList.remove('embla__dot--selected')
          dotNodes[selected].classList.add('embla__dot--selected')
        }

        emblaApi
          .on('init', addDotBtnsWithClickHandlers)
          .on('reInit', addDotBtnsWithClickHandlers)
          .on('init', toggleDotBtnsActive)
          .on('reInit', toggleDotBtnsActive)
          .on('select', toggleDotBtnsActive)

        return () => {
          dotsNode.innerHTML = ''
        }
      }

      // Main Carousel Setup
      const OPTIONS = { loop: true }

      const emblaNode = document.querySelector('.embla')
      const viewportNode = emblaNode.querySelector('.embla__viewport')
      const prevBtnNode = emblaNode.querySelector('.embla__button--prev')
      const nextBtnNode = emblaNode.querySelector('.embla__button--next')
      const dotsNode = emblaNode.querySelector('.embla__dots')

      const emblaApi = EmblaCarousel(viewportNode, OPTIONS, [EmblaCarouselAutoplay()])

      const onNavButtonClick = (emblaApi) => {
        const autoplay = emblaApi?.plugins()?.autoplay
        if (!autoplay) return

        const resetOrStop =
          autoplay.options.stopOnInteraction === false
            ? autoplay.reset
            : autoplay.stop

        resetOrStop()
      }

      const removePrevNextBtnsClickHandlers = addPrevNextBtnsClickHandlers(
        emblaApi,
        prevBtnNode,
        nextBtnNode,
        onNavButtonClick
      )
      const removeDotBtnsAndClickHandlers = addDotBtnsAndClickHandlers(
        emblaApi,
        dotsNode,
        onNavButtonClick
      )

      emblaApi.on('destroy', removePrevNextBtnsClickHandlers)
      emblaApi.on('destroy', removeDotBtnsAndClickHandlers)
    })
  </script>